import { CommonModule } from '@angular/common';
import { Compo<PERSON>, <PERSON><PERSON><PERSON>roy, OnInit, inject } from '@angular/core';
import { Form<PERSON>rray, FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { Store } from '@ngrx/store';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { CheckboxModule } from 'primeng/checkbox';
import { DatePickerModule } from 'primeng/datepicker';
import { IconFieldModule } from 'primeng/iconfield';
import { InputIconModule } from 'primeng/inputicon';
import { InputTextModule } from 'primeng/inputtext';
import { SelectModule } from 'primeng/select';
import { SelectButtonModule } from 'primeng/selectbutton';
import { TabsModule } from 'primeng/tabs';
import { TooltipModule } from 'primeng/tooltip';
import { Observable, Subscription } from 'rxjs';

import { SearchService } from 'app/services/search/search.service';
import * as SearchActions from 'app/store/search/search.actions';
import * as fromSearch from 'app/store/search/search.selectors';

interface SelectOption {
  label: string;
  value: string;
}

/**
 * Custom validator to ensure start date is before end date
 */
function dateRangeValidator(control: any): any {
  const startDate = control.get('startDate')?.value;
  const endDate = control.get('endDate')?.value;

  if (!startDate || !endDate) {
    return null; // Don't validate if either date is missing
  }

  const start = new Date(startDate);
  const end = new Date(endDate);

  if (start >= end) {
    return { dateRangeInvalid: { message: 'Start date must be before end date' } };
  }

  return null;
}

@Component({
  selector: 'app-home',
  imports: [
    RouterModule,
    CommonModule,
    ReactiveFormsModule,
    TabsModule,
    CheckboxModule,
    InputTextModule,
    SelectModule,
    ButtonModule,
    CardModule,
    IconFieldModule,
    InputIconModule,
    SelectButtonModule,
    TooltipModule,
    DatePickerModule,
  ],
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss'],
  standalone: true,
})
export class HomeComponent implements OnInit, OnDestroy {
  searchTypeOptions: SelectOption[] = [
    { label: 'EDI Record', value: 'edi' },
    { label: 'App Record', value: 'app' },
  ];
  selectedSearchType: string = 'edi';

  directionOptions: SelectOption[] = [
    { label: 'Inbound & Outbound', value: 'inbound-outbound' },
    { label: 'Inbound', value: 'inbound' },
    { label: 'Outbound', value: 'outbound' },
  ];
  selectedDirection: string = 'inbound-outbound';

  transactionViewOptions: SelectOption[] = [
    { label: 'All Transaction Sets', value: 'all' },
    { label: 'Group View', value: 'group' },
  ];

  selectedTransactionView = 'all';
  selectedEquipmentSearchType = 'equipment';

  inboundFields = [
    { label: 'ISA Sender', placeholder: 'ISA Sender', value: '', key: 'isaSender' },
    { label: 'GS Sender', placeholder: 'GS Sender', value: '', key: 'gsSender' },
  ];

  outboundFields = [
    { label: 'ISA Receiver', placeholder: 'ISA Receiver', value: '', key: 'isaReceiver' },
    { label: 'GS Receiver', placeholder: 'GS Receiver', value: '', key: 'gsReceiver' },
  ];

  // Additional filter options
  additionalInboundOptions = [
    { label: 'ISA Receiver', value: 'isaReceiver' },
    { label: 'GS Receiver', value: 'gsReceiver' },
  ];

  additionalOutboundOptions = [
    { label: 'ISA Sender', value: 'isaSender' },
    { label: 'GS Sender', value: 'gsSender' },
  ];

  additionalEdiAttributesOptions = [
    { label: 'Message ID', value: 'messageId' },
    { label: 'Network', value: 'network' },
    { label: 'Message Status', value: 'messageStatus' },
    { label: 'Control Number', value: 'controlNumber' },
  ];

  // Date range for the search form - now using separate start and end dates
  // Initialize end date as current time
  endDate: Date = new Date();
  // Initialize start date as 15 days ago from current time
  startDate: Date = new Date(new Date().setDate(new Date().getDate() - 15));

  // Form group for the search form
  searchForm!: FormGroup;

  // Local component state for show/hide of additional filters (not persisted)
  showAdditionalInboundFilters = {
    isaReceiver: false,
    gsReceiver: false,
  };

  showAdditionalOutboundFilters = {
    isaSender: false,
    gsSender: false,
  };

  showAdditionalEdiAttributesFilters = {
    messageId: false,
    network: false,
    messageStatus: false,
    controlNumber: false,
  };

  // Loading state
  isLoading = false;

  // NgRx selectors
  formValues$: Observable<any> = inject(Store).select(fromSearch.selectFormValues);
  hasSearched$: Observable<boolean> = inject(Store).select(fromSearch.selectHasSearched);

  // Subscriptions
  private subscriptions: Subscription = new Subscription();

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private fb: FormBuilder,
    private store: Store,
    // Inject SearchService for potential future direct API calls
    private searchService: SearchService,
  ) {}

  ngOnInit() {
    this.initForm();

    // Delay store subscription to ensure form is fully initialized
    setTimeout(() => {
      // Subscribe to form values from the store
      this.subscriptions.add(
        this.formValues$.subscribe(formValues => {
          if (formValues && this.searchForm) {
            // Update the form with values from the store
            this.updateFormFromState(formValues);
          }
        }),
      );
    }, 0);

    // Subscribe to query parameters to handle direct URL navigation
    this.subscriptions.add(
      this.route.queryParams.subscribe(params => {
        if (Object.keys(params).length > 0 && this.searchForm) {
          // Extract dynamic input values from query parameters (show/hide state is not persisted)
          const formValues: any = {};

          // Process dynamic input values from query parameters
          const dynamicInboundInputs: any = {};
          const dynamicOutboundInputs: any = {};
          const dynamicEdiAttributesInputs: any = {};

          Object.keys(params).forEach(key => {
            // Process dynamic inbound input values
            if (key.startsWith('filter_dynamic_inbound_')) {
              const inputKey = key.replace('filter_dynamic_inbound_', '');
              dynamicInboundInputs[inputKey] = params[key];
            }
            // Process dynamic outbound input values
            else if (key.startsWith('filter_dynamic_outbound_')) {
              const inputKey = key.replace('filter_dynamic_outbound_', '');
              dynamicOutboundInputs[inputKey] = params[key];
            }
            // Process dynamic EDI attributes input values
            else if (key.startsWith('filter_dynamic_edi_')) {
              const inputKey = key.replace('filter_dynamic_edi_', '');
              dynamicEdiAttributesInputs[inputKey] = params[key];
            }
          });

          // Only update the form if we have dynamic input values
          if (
            Object.keys(dynamicInboundInputs).length > 0 ||
            Object.keys(dynamicOutboundInputs).length > 0 ||
            Object.keys(dynamicEdiAttributesInputs).length > 0
          ) {
            formValues.dynamicInboundInputs = dynamicInboundInputs;
            formValues.dynamicOutboundInputs = dynamicOutboundInputs;
            formValues.dynamicEdiAttributesInputs = dynamicEdiAttributesInputs;

            // Update the form with the extracted values
            this.updateFormFromState(formValues);

            // Auto-show filters that have values from URL
            Object.keys(dynamicInboundInputs).forEach(key => {
              if (dynamicInboundInputs[key] && dynamicInboundInputs[key].trim()) {
                this.showAdditionalInboundFilters[key as keyof typeof this.showAdditionalInboundFilters] = true;
              }
            });

            Object.keys(dynamicOutboundInputs).forEach(key => {
              if (dynamicOutboundInputs[key] && dynamicOutboundInputs[key].trim()) {
                this.showAdditionalOutboundFilters[key as keyof typeof this.showAdditionalOutboundFilters] = true;
              }
            });

            Object.keys(dynamicEdiAttributesInputs).forEach(key => {
              if (dynamicEdiAttributesInputs[key] && dynamicEdiAttributesInputs[key].trim()) {
                this.showAdditionalEdiAttributesFilters[key as keyof typeof this.showAdditionalEdiAttributesFilters] = true;
              }
            });
          }
        }
      }),
    );
  }

  ngOnDestroy() {
    // Clean up subscriptions
    this.subscriptions.unsubscribe();
  }

  /**
   * Initialize the reactive form
   */
  private initForm(): void {
    // Create form arrays for inbound and outbound fields
    const inboundFieldsArray = this.fb.array(
      this.inboundFields.map(field =>
        this.fb.nonNullable.group({
          label: field.label,
          placeholder: field.placeholder,
          value: '',
          key: field.key,
        }),
      ),
    );

    const outboundFieldsArray = this.fb.array(
      this.outboundFields.map(field =>
        this.fb.nonNullable.group({
          label: field.label,
          placeholder: field.placeholder,
          value: '',
          key: field.key,
        }),
      ),
    );

    // Create the main form group
    this.searchForm = this.fb.nonNullable.group(
      {
        searchType: this.selectedSearchType,
        startDate: this.startDate,
        endDate: this.endDate,
        direction: this.selectedDirection,
        transactionView: this.selectedTransactionView,
        equipmentSearchType: this.selectedEquipmentSearchType,
        equipmentIds: '',
        transactionAttributes: '',
        inboundFields: inboundFieldsArray,
        outboundFields: outboundFieldsArray,
        // Dynamic text inputs for additional filters (values persisted to store)
        dynamicInboundInputs: this.fb.nonNullable.group({
          isaReceiver: '',
          gsReceiver: '',
        }),
        dynamicOutboundInputs: this.fb.nonNullable.group({
          isaSender: '',
          gsSender: '',
        }),
        dynamicEdiAttributesInputs: this.fb.nonNullable.group({
          messageId: '',
          network: '',
          messageStatus: '',
          controlNumber: '',
        }),
      },
      { validators: [dateRangeValidator] },
    );

    // Subscribe to date changes to provide real-time validation feedback
    this.searchForm.get('startDate')?.valueChanges.subscribe(() => {
      this.searchForm.updateValueAndValidity();
    });

    this.searchForm.get('endDate')?.valueChanges.subscribe(() => {
      this.searchForm.updateValueAndValidity();
    });

    // Note: Show/hide state is now managed locally in component memory
  }

  /**
   * Clear input values when filters are hidden
   */
  private clearInputWhenHidden(filterType: 'inbound' | 'outbound' | 'edi', key: string): void {
    let formGroupName: string;
    let isVisible: boolean;

    switch (filterType) {
      case 'inbound':
        formGroupName = 'dynamicInboundInputs';
        isVisible = this.showAdditionalInboundFilters[key as keyof typeof this.showAdditionalInboundFilters];
        break;
      case 'outbound':
        formGroupName = 'dynamicOutboundInputs';
        isVisible = this.showAdditionalOutboundFilters[key as keyof typeof this.showAdditionalOutboundFilters];
        break;
      case 'edi':
        formGroupName = 'dynamicEdiAttributesInputs';
        isVisible = this.showAdditionalEdiAttributesFilters[key as keyof typeof this.showAdditionalEdiAttributesFilters];
        break;
    }

    if (!isVisible) {
      this.searchForm.get(`${formGroupName}.${key}`)?.setValue('');
    }
  }

  // Getters for form arrays to use in template
  get inboundFieldsFormArray(): FormArray | null {
    return this.searchForm?.get('inboundFields') as FormArray | null;
  }

  get outboundFieldsFormArray(): FormArray | null {
    return this.searchForm?.get('outboundFields') as FormArray | null;
  }

  // Getter to check for date range validation errors
  get hasDateRangeError(): boolean {
    return this.searchForm?.hasError('dateRangeInvalid') || false;
  }

  // Getter to get the date range error message
  get dateRangeErrorMessage(): string {
    const error = this.searchForm?.getError('dateRangeInvalid');
    return error?.message || '';
  }

  // TrackBy functions for better performance
  trackByValue = (_: number, item: any) => item.value;

  // Helper methods to check show/hide states (using local component state)
  isInboundCheckboxChecked(key: string): boolean {
    return this.showAdditionalInboundFilters[key as keyof typeof this.showAdditionalInboundFilters] || false;
  }

  isOutboundCheckboxChecked(key: string): boolean {
    return this.showAdditionalOutboundFilters[key as keyof typeof this.showAdditionalOutboundFilters] || false;
  }

  isEdiCheckboxChecked(key: string): boolean {
    return this.showAdditionalEdiAttributesFilters[key as keyof typeof this.showAdditionalEdiAttributesFilters] || false;
  }

  // Helper methods to check if any checkboxes are checked (for conditional styling)
  hasAnyInboundChecked(): boolean {
    return Object.values(this.showAdditionalInboundFilters).some(value => value);
  }

  hasAnyOutboundChecked(): boolean {
    return Object.values(this.showAdditionalOutboundFilters).some(value => value);
  }

  hasAnyEdiAttributesChecked(): boolean {
    return Object.values(this.showAdditionalEdiAttributesFilters).some(value => value);
  }

  // Methods to toggle show/hide state and clear inputs when hidden
  toggleInboundFilter(key: string): void {
    const typedKey = key as keyof typeof this.showAdditionalInboundFilters;
    this.showAdditionalInboundFilters[typedKey] = !this.showAdditionalInboundFilters[typedKey];
    this.clearInputWhenHidden('inbound', key);
  }

  toggleOutboundFilter(key: string): void {
    const typedKey = key as keyof typeof this.showAdditionalOutboundFilters;
    this.showAdditionalOutboundFilters[typedKey] = !this.showAdditionalOutboundFilters[typedKey];
    this.clearInputWhenHidden('outbound', key);
  }

  toggleEdiAttributesFilter(key: string): void {
    const typedKey = key as keyof typeof this.showAdditionalEdiAttributesFilters;
    this.showAdditionalEdiAttributesFilters[typedKey] = !this.showAdditionalEdiAttributesFilters[typedKey];
    this.clearInputWhenHidden('edi', key);
  }

  /**
   * Submit the search form
   */
  onSearch(): void {
    if (!this.searchForm.valid) {
      // Mark all fields as touched to show validation errors
      this.searchForm.markAllAsTouched();

      // If there's a date range error, we could show a toast or alert here
      if (this.hasDateRangeError) {
        console.warn('Date range validation error:', this.dateRangeErrorMessage);
      }

      return;
    }

    this.isLoading = true;
    const formValues = this.searchForm.value;

    // Save form values to the store
    this.store.dispatch(SearchActions.saveFormValues({ payload: formValues }));

    // Build query parameters
    const queryParams: Record<string, string> = {
      page: '1',
      pageSize: '20',
      sortBy: 'dateReceived',
      sortDirection: 'desc',
    };

    // Add search type filter
    if (formValues.searchType) {
      queryParams['filter_searchType'] = formValues.searchType;
    }

    // Add direction filter
    if (formValues.direction) {
      queryParams['filter_direction'] = formValues.direction;
    }

    // Add transaction view filter
    if (formValues.transactionView) {
      queryParams['filter_transactionView'] = formValues.transactionView;
    }

    // Add equipment IDs if present
    if (formValues.equipmentIds && formValues.equipmentIds.trim()) {
      queryParams['filter_equipmentIds'] = formValues.equipmentIds.trim();
      queryParams['filter_equipmentSearchType'] = formValues.equipmentSearchType;
    }

    // Add inbound fields if present
    formValues.inboundFields.forEach((field: any) => {
      if (field.value && field.value.trim()) {
        queryParams[`filter_${field.key}`] = field.value.trim();
      }
    });

    // Add outbound fields if present
    formValues.outboundFields.forEach((field: any) => {
      if (field.value && field.value.trim()) {
        queryParams[`filter_${field.key}`] = field.value.trim();
      }
    });

    // Add transaction attributes if present
    if (formValues.transactionAttributes && formValues.transactionAttributes.trim()) {
      queryParams['filter_transactionAttributes'] = formValues.transactionAttributes.trim();
    }

    // Note: Show/hide state is not persisted in URL - only the actual input values are persisted

    // Add dynamic input values for checked additional filters
    if (formValues.dynamicInboundInputs) {
      Object.entries(formValues.dynamicInboundInputs).forEach(([key, value]) => {
        if (value && typeof value === 'string' && value.trim()) {
          queryParams[`filter_dynamic_inbound_${key}`] = value.trim();
        }
      });
    }

    if (formValues.dynamicOutboundInputs) {
      Object.entries(formValues.dynamicOutboundInputs).forEach(([key, value]) => {
        if (value && typeof value === 'string' && value.trim()) {
          queryParams[`filter_dynamic_outbound_${key}`] = value.trim();
        }
      });
    }

    if (formValues.dynamicEdiAttributesInputs) {
      Object.entries(formValues.dynamicEdiAttributesInputs).forEach(([key, value]) => {
        if (value && typeof value === 'string' && value.trim()) {
          queryParams[`filter_dynamic_edi_${key}`] = value.trim();
        }
      });
    }

    // Add start and end dates
    if (formValues.startDate) {
      const startMs = formValues.startDate.getTime();
      queryParams['filter_dateRangeStart'] = startMs.toString();
    }

    if (formValues.endDate) {
      const endMs = formValues.endDate.getTime();
      queryParams['filter_dateRangeEnd'] = endMs.toString();
    }

    // Save search parameters to the store
    this.store.dispatch(SearchActions.saveSearchParams({ payload: queryParams }));

    // Navigate to search results with query parameters
    this.router.navigate(['/search-results'], { queryParams });

    // Also call API directly to pre-fetch results
    const searchParams = this.searchService.parseSearchParams(queryParams);
    this.searchService.search(searchParams).subscribe(response => {
      this.isLoading = false;
      console.log('Search response:', response);
    });
  }

  /**
   * Reset the search form
   */
  onReset(): void {
    // Reset the form to initial values using the form's reset method
    this.searchForm.reset({
      searchType: 'edi',
      direction: 'inbound-outbound',
      transactionView: 'all',
      equipmentSearchType: 'equipment',
      equipmentIds: '',
      transactionAttributes: '',
      startDate: new Date(new Date().setDate(new Date().getDate() - 15)),
      endDate: new Date(),
      // Include empty arrays for the form arrays to properly reset them
      inboundFields: this.inboundFields.map(field => ({
        label: field.label,
        placeholder: field.placeholder,
        value: '',
        key: field.key,
      })),
      outboundFields: this.outboundFields.map(field => ({
        label: field.label,
        placeholder: field.placeholder,
        value: '',
        key: field.key,
      })),

      // Reset dynamic inputs
      dynamicInboundInputs: {
        isaReceiver: '',
        gsReceiver: '',
      },
      dynamicOutboundInputs: {
        isaSender: '',
        gsSender: '',
      },
      dynamicEdiAttributesInputs: {
        messageId: '',
        network: '',
        messageStatus: '',
        controlNumber: '',
      },
    });

    // Reset the store state
    this.store.dispatch(SearchActions.resetState());

    // Reset local show/hide state
    this.showAdditionalInboundFilters = {
      isaReceiver: false,
      gsReceiver: false,
    };

    this.showAdditionalOutboundFilters = {
      isaSender: false,
      gsSender: false,
    };

    this.showAdditionalEdiAttributesFilters = {
      messageId: false,
      network: false,
      messageStatus: false,
      controlNumber: false,
    };
  }

  /**
   * Update form values from the state
   */
  private updateFormFromState(formValues: any): void {
    if (!formValues) return;

    // Note: Show/hide state is managed locally and not persisted to store

    // Only update the specific controls that exist in formValues, don't override everything
    const updateData: any = {};

    if (formValues.searchType !== undefined) updateData.searchType = formValues.searchType;
    if (formValues.direction !== undefined) updateData.direction = formValues.direction;
    if (formValues.transactionView !== undefined) updateData.transactionView = formValues.transactionView;
    if (formValues.equipmentSearchType !== undefined) updateData.equipmentSearchType = formValues.equipmentSearchType;
    if (formValues.equipmentIds !== undefined) updateData.equipmentIds = formValues.equipmentIds;
    if (formValues.transactionAttributes !== undefined) updateData.transactionAttributes = formValues.transactionAttributes;
    if (formValues.startDate !== undefined) updateData.startDate = formValues.startDate;
    if (formValues.endDate !== undefined) updateData.endDate = formValues.endDate;

    // Update dynamic inputs if they exist in formValues
    if (formValues.dynamicInboundInputs !== undefined) {
      updateData.dynamicInboundInputs = formValues.dynamicInboundInputs;
    }
    if (formValues.dynamicOutboundInputs !== undefined) {
      updateData.dynamicOutboundInputs = formValues.dynamicOutboundInputs;
    }
    if (formValues.dynamicEdiAttributesInputs !== undefined) {
      updateData.dynamicEdiAttributesInputs = formValues.dynamicEdiAttributesInputs;
    }

    // Update main form controls
    this.searchForm.patchValue(updateData, { emitEvent: false });

    // Update inbound fields
    if (formValues.inboundFields && formValues.inboundFields.length > 0 && this.inboundFieldsFormArray) {
      formValues.inboundFields.forEach((field: any, index: number) => {
        if (index < this.inboundFieldsFormArray!.length) {
          this.inboundFieldsFormArray!.at(index).patchValue(
            {
              value: field.value,
            },
            { emitEvent: false },
          );
        }
      });
    }

    // Update outbound fields
    if (formValues.outboundFields && formValues.outboundFields.length > 0 && this.outboundFieldsFormArray) {
      formValues.outboundFields.forEach((field: any, index: number) => {
        if (index < this.outboundFieldsFormArray!.length) {
          this.outboundFieldsFormArray!.at(index).patchValue(
            {
              value: field.value,
            },
            { emitEvent: false },
          );
        }
      });
    }
  }
}
